<?php

namespace App\Http\Resources\Shopping;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Request;
use App\Http\Resources\Product\ProductAttributesResource;

/**
 * Cart Item Resource
 */
class CartItemResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $variant = $this->whenLoaded('productVariant');
        $variant = $variant instanceof \App\Models\Product\ProductVariant ? $variant : null;

        $product = optional($variant)->relationLoaded('product')
            ? $variant->product
            : null;
        $attributes = $variant && $variant->relationLoaded('attributes')
            ? $variant->attributes
            : collect();

        return [
            'product_id' => $this->product_id,
            'id' => $this->product_variant_id,
            'name' => $this->name,
            'sku' => $variant ? $variant->sku : null,
            'price' => $this->price,
            'sale_price' => $this->sale_price,
            'price_unit' => $this->whenLoaded('priceUnit', fn() => $this->priceUnit?->name),
            'quantity' => $this->quantity,
            'discount' => $this->discount,
            'total' => $this->total,
            'image' => $product && $product->gallery()->first() ? $product->gallery()->first()->image_url : null,
            'shop_name' => $this->shop_name,
            'in_stock' => $variant ? $variant->current_quantity >= $this->quantity : false,
            "max_quantity" => $variant ? $variant->stock : 0,
            "attributes" => $variant ? ProductAttributesResource::collection($attributes) : [],
        ];
    }
}
