<?php

namespace App\Models\Product;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * PriceUnit Model
 * 
 * Represents currency/price units in the system (تومان, ریال, دلار, etc.)
 * This is separate from ProductUnit which handles measurement units.
 */
class PriceUnit extends Model
{
    protected $fillable = [
        'name',
        'symbol', 
        'code',
        'is_active',
        'is_default'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_default' => 'boolean',
    ];

    /**
     * Get the default price unit.
     */
    public static function getDefault(): ?self
    {
        return static::where('is_default', true)->where('is_active', true)->first();
    }

    /**
     * Set this price unit as the default.
     */
    public function setAsDefault(): void
    {
        // Remove default from all other units
        static::where('is_default', true)->update(['is_default' => false]);
        
        // Set this unit as default
        $this->update(['is_default' => true, 'is_active' => true]);
    }

    /**
     * Scope to get only active price units.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * A price unit can be used by many product variants.
     */
    public function productVariants(): HasMany
    {
        return $this->hasMany(ProductVariant::class, 'price_unit_id');
    }

    /**
     * A price unit can be used by many guarantees.
     */
    public function guarantees(): HasMany
    {
        return $this->hasMany(Guarantee::class, 'price_unit_id');
    }
}
